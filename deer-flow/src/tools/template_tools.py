# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

"""
template_tools.py - 简化版

简化的模板工具，只保留核心的use_template功能。
采用工程化方式，模板ID由外部系统传入，不依赖AI选择。
"""

from typing import Dict, Any, Optional
from langchain_core.tools import tool
from pydantic import BaseModel, Field

from src.graph_v2.template_models import PlanTemplate
from src.graph_v2.template_renderer import template_renderer, TemplateRenderError
from src.graph_v2.unified_models import UnifiedPlan

# 全局模板注册表
_template_registry: Dict[str, PlanTemplate] = {}


class UseTemplateInput(BaseModel):
    template_id: str = Field(description="模板标识符")
    params: Dict[str, Any] = Field(description="模板参数")
    user_context: Optional[str] = Field(default=None, description="可选的用户上下文描述")


@tool("use_template", args_schema=UseTemplateInput)
def use_template(template_id: str, params: Dict[str, Any], user_context: Optional[str] = None) -> Dict[str, Any]:
    """
    使用指定模板创建执行计划
    
    适用于已知模板ID的场景，通常由上层系统或用户界面传入。
    这是工程化的模板使用方式，不依赖AI进行模板选择。
    
    常用模板ID：
    - "city_poster_series": 城市海报系列
    - "ai_parody_video": AI鬼畜视频制作
    - "brand_design_suite": 品牌设计套件
    
    Args:
        template_id: 模板标识符（如 "city_poster_series"）
        params: 模板参数（如 {"character": "哪吒", "style": "modern"}）
        user_context: 可选的用户上下文描述
        
    Returns:
        {
            "success": bool,
            "plan": UnifiedPlan,
            "template_used": str,
            "error": Optional[str],
            "metadata": Dict[str, Any]
        }
    """
    try:
        # 获取模板
        template = _template_registry.get(template_id)
        if not template:
            available_templates = list(_template_registry.keys())
            return {
                "success": False,
                "plan": None,
                "template_used": template_id,
                "error": f"模板 '{template_id}' 不存在",
                "available_templates": available_templates
            }
        
        # 使用模板渲染器创建计划
        try:
            plan = template_renderer.render_plan(template, params)
            
            # 如果提供了用户上下文，更新任务描述
            if user_context:
                plan.original_task = user_context
            
        except TemplateRenderError as e:
            return {
                "success": False,
                "plan": None,
                "template_used": template_id,
                "error": f"模板渲染失败: {str(e)}"
            }
        
        # 更新模板使用统计
        template.usage_count += 1
        
        print(f"📋 use_template成功: {template_id} -> {len(plan.steps)}个步骤")
        
        return {
            "success": True,
            "plan": plan,
            "template_used": template_id,
            "error": None,
            "metadata": {
                "template_name": template.name,
                "parameters_used": params,
                "total_steps": len(plan.steps),
                "estimated_duration": template.estimated_duration
            }
        }
        
    except Exception as e:
        return {
            "success": False,
            "plan": None,
            "template_used": template_id,
            "error": f"使用模板时出现错误: {str(e)}"
        }


def register_template(template: PlanTemplate):
    """注册模板到全局注册表"""
    _template_registry[template.template_id] = template
    print(f"📝 注册模板: {template.template_id} - {template.name}")


def get_registered_templates() -> Dict[str, str]:
    """获取已注册的模板列表（用于调试）"""
    return {tid: template.name for tid, template in _template_registry.items()}


# 导出的工具列表
template_tools = [use_template]
