# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

from typing import Literal, Optional
from datetime import datetime
import json
import logging

from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder
from pydantic.v1 import BaseModel, Field
from langchain_core.messages import AIMessage
from langgraph.prebuilt import create_react_agent
from langchain_core.output_parsers.pydantic import PydanticOutputParser

from src.prompts.template import get_prompt_template, apply_prompt_template
from .factory import create_agent  # 现在可以安全导入，因为__init__.py不再导入agents
from src.tools import (
    audio_tools,
    video_tools,
    visual_tools,
    suno_tool,
)

from src.llms.llm import get_llm_by_type
from src.config.agents import AGENT_LLM_MAP

print("<<<<< RELOADING AGENTS MODULE >>>>>")

logger = logging.getLogger(__name__)


# Define the structured output for the Coordinator Agent
class Decision(BaseModel):
    """A decision on how to proceed."""
    next_step: str = Field(description="Either 'planner' for complex tasks, 'delegate_to_agent' for simple, one-step tasks, 'execute_plan' to start an approved plan, or 'direct_reply' for conversation.")
    response: Optional[str] = Field(default=None, description="If next_step is 'direct_reply', this field contains the response to the user.")
    target_agent: Optional[str] = Field(default=None, description="If next_step is 'delegate_to_agent', this specifies the agent node to use (e.g., 'visual_agent').")
    task_description: Optional[str] = Field(default=None, description="If next_step is 'delegate_to_agent', this is the detailed instruction for the target agent.")


class FinalResponse(BaseModel):
    """The final response to the user, summarizing the workflow."""
    response: str = Field(description="The final, user-facing response summarising the results of the workflow.")


def robust_pydantic_parser(model, max_retries=3):
    """
    A robust Pydantic parser that can handle JSON decoding errors and retry.
    """
    def parse(ai_message: AIMessage) -> BaseModel:
        for i in range(max_retries):
            try:
                # The actual parsing logic
                pydantic_parser = PydanticOutputParser(pydantic_object=model)
                return pydantic_parser.parse(ai_message.content)
            except (json.JSONDecodeError, ValueError) as e:
                # This indicates the LLM's output is not valid JSON.
                logger.warning(f"Pydantic parsing failed (attempt {i+1}/{max_retries}): {e}")
                # Fallback for the final attempt
                if i == max_retries - 1:
                    logger.error("Final parsing attempt failed. Returning a default object.")
                    if model == Decision:
                        return Decision(next_step="direct_reply", response="I'm not sure how to help with that.")
                    else:
                        # A generic fallback
                        return BaseModel() 
        # This line should ideally not be reached
        raise ValueError("Failed to parse after multiple retries.")
    return parse


def coordinator_agent():
    """
    The coordinator agent decides whether to respond directly to the user or
    to start a planning process. This is the simplified, standard implementation.
    """
    llm = get_llm_by_type(AGENT_LLM_MAP.get("coordinator", "default_chat_model"))
    # Bind the Decision tool to the LLM, allowing it to choose whether to use it or chat directly.
    # This is more flexible than with_structured_output, which forces a tool call.
    tool_bound_llm = llm.bind_tools([Decision])
    
    system_prompt_template = get_prompt_template("coordinator")
    system_prompt = system_prompt_template.replace(
        "{{ CURRENT_TIME }}", datetime.now().strftime("%a %b %d %Y %H:%M:%S %z")
    )

    prompt = ChatPromptTemplate.from_messages(
        [
            ("system", system_prompt),
            MessagesPlaceholder(variable_name="messages"),
        ]
    )
    
    # The agent chain now allows for either a direct AIMessage response or a tool call.
    agent = prompt | tool_bound_llm
    
    return agent


def create_responder_agent():
    """
    Creates the responder agent that generates the final summary response to the user.
    This agent now outputs raw text directly for robustness.
    """
    raw_prompt_template = get_prompt_template("responder_zh")
    prompt = ChatPromptTemplate.from_messages([
        ("system", raw_prompt_template),
        ("human", "{report_data}")
    ])
    
    llm = get_llm_by_type("basic")

    # The responder agent's job is to generate a natural language response
    # based on the final state of the graph.
    # We no longer parse to JSON, making it much more robust.
    responder_chain = prompt | llm
    return responder_chain


# Create agents using configured LLM types
# The factory function has been moved to factory.py to avoid circular imports.

# Create agents using the factory function
# Temporarily comment out unused agents
# research_agent = create_agent(
#     "researcher",
#     "researcher",
#     [web_search_tool, crawl_tool, vector_db_search, vector_db_add_text, suno_tool],
#     "researcher"
# )
# coder_agent = create_agent("coder", "coder", [python_repl_tool], "coder")

# # New Artistic Agents
# concept_artist_agent = create_agent(
#     "concept_artist",
#     "concept_artist",
#     [web_search_tool], # Concept artist might use web search for inspiration
#     "concept_artist_prompt" # Corresponds to concept_artist_prompt.md
# )

audio_creator_agent = create_agent(
    "audio_creator",
    "audio_creator",
    audio_tools, # <--- Pass the complete list of audio tools
    "audio_creator_prompt" # Corresponds to a new audio_creator_prompt.md
)

# motion_artist_agent = create_agent(
#     "motion_artist",
#     "motion_artist",
#     [], # Primarily uses MCP tools for animation
#     "motion_artist_prompt" # Corresponds to motion_artist_prompt.md
# )

# post_processor_agent = create_agent(
#     "post_processor",
#     "post_processor",
#     [], # Primarily uses MCP tools or describes steps
#     "post_processor_prompt" # Corresponds to post_processor_prompt.md
# )

video_creator_agent = create_agent(
    "video_creator",
    "video_creator",
    video_tools, # Provide the video tools
    "video_creator_prompt"
)

visual_creator_agent = create_agent(
    "visual_creator",
    "visual_creator",
    visual_tools, # Provide the visual tools
    "visual_creator_prompt"
)

# Alias the agents to the names expected by nodes.py
visual_agent = visual_creator_agent
audio_agent = audio_creator_agent
video_agent = video_creator_agent
