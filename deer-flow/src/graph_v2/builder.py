# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

"""
Graph Builder for the V2 architecture.

This file defines the function to create and compile the new, simplified
Master Agent-centric workflow.
"""

from langgraph.graph import StateGraph, END
from langchain_core.messages import AIMessage

from .types import State
from .nodes import master_agent_node
from .execution_engine import execution_engine_node, should_use_execution_engine

def should_continue(state: State) -> str:
    """
    智能路由器：决定使用Master Agent还是执行引擎

    路由逻辑：
    1. 首先检查是否有工具调用需要继续
    2. 然后检查是否有新创建的多步计划需要执行引擎
    3. 最后决定是否结束对话
    """
    messages = state["messages"]
    last_message = messages[-1]
    plan = state.get("plan")

    # 如果有工具调用，继续让Master Agent处理
    if isinstance(last_message, AIMessage) and last_message.tool_calls:
        return "master_agent"

    # 检查是否有新创建的多步计划需要执行引擎
    if should_use_execution_engine(state):
        # 多步计划且未完成 → 使用执行引擎
        if plan and not plan.is_complete():
            print("🔄 路由决策: 发现多步计划，切换到执行引擎")
            return "execution_engine"

    # 对话结束条件：AI消息且无工具调用
    if isinstance(last_message, AIMessage) and not last_message.tool_calls:
        return END

    # 默认继续Master Agent
    return "master_agent"


class GraphBuilder:
    """
    Builds the V2 workflow graph with execution engine support.

    This class creates a graph with two main nodes:
    - master_agent: Handles conversation and single-step tasks
    - execution_engine: Drives multi-step plan execution
    """
    def __init__(self):
        self.builder = StateGraph(State)

        # 添加节点
        self.builder.add_node("master_agent", master_agent_node)
        self.builder.add_node("execution_engine", execution_engine_node)

        # 设置入口点
        self.builder.set_entry_point("master_agent")

        # 添加条件边：从master_agent路由到不同节点
        self.builder.add_conditional_edges(
            "master_agent",
            should_continue,
            {
                "master_agent": "master_agent",
                "execution_engine": "execution_engine",
                "__end__": END
            }
        )

        # 执行引擎完成后直接结束
        self.builder.add_edge("execution_engine", END)

    def build(self, checkpointer):
        """
        Compiles the graph into a runnable, attaching the provided checkpointer.
        """
        return self.builder.compile(checkpointer=checkpointer) 