# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

"""
template_renderer.py

改进的模板渲染器，使用Jinja2提供稳定可靠的模板实例化功能。

核心改进：
1. 使用Jinja2模板引擎替代简单字符串替换
2. 完善的参数验证和错误处理
3. 支持复杂的模板逻辑
4. 与UnifiedPlan深度集成
"""

from typing import Dict, Any, List, Optional
from jinja2 import Environment, Template, TemplateError, StrictUndefined
import uuid
from datetime import datetime

from .unified_models import UnifiedPlan, UnifiedStep
from .template_models import PlanTemplate, StepTemplate, ParameterSchema


class TemplateRenderError(Exception):
    """模板渲染错误"""
    pass


class UnifiedTemplateRenderer:
    """
    统一的模板渲染器，专为UnifiedPlan设计。
    
    特性：
    - 使用Jinja2模板引擎
    - 严格的参数验证
    - 详细的错误报告
    - 支持复杂模板逻辑
    """
    
    def __init__(self):
        # 配置Jinja2环境
        self.env = Environment(
            undefined=StrictUndefined,  # 严格模式，未定义变量会报错
            trim_blocks=True,          # 移除块后的换行
            lstrip_blocks=True         # 移除块前的空白
        )
        
        # 添加自定义过滤器
        self.env.filters['default_if_none'] = lambda x, default: default if x is None else x
        self.env.filters['safe_str'] = lambda x: str(x) if x is not None else ""
    
    def render_plan(self, template: PlanTemplate, params: Dict[str, Any]) -> UnifiedPlan:
        """
        渲染模板为UnifiedPlan。
        
        Args:
            template: 计划模板
            params: 模板参数
            
        Returns:
            渲染后的UnifiedPlan
            
        Raises:
            TemplateRenderError: 渲染失败时抛出
        """
        try:
            # 验证参数
            validated_params = self._validate_params(template, params)
            
            # 渲染步骤
            rendered_steps = []
            for step_template in template.step_templates:
                rendered_step = self._render_step(step_template, validated_params)
                rendered_steps.append(rendered_step)
            
            # 生成计划ID
            plan_id = f"{template.template_id}_{uuid.uuid4().hex[:8]}"
            
            # 渲染任务描述
            task_template = self.env.from_string(template.description)
            original_task = task_template.render(**validated_params)
            
            # 创建UnifiedPlan
            plan = UnifiedPlan(
                plan_id=plan_id,
                original_task=original_task,
                steps=rendered_steps,
                template_id=template.template_id,
                template_params=validated_params,
                is_from_template=True
            )
            
            return plan
            
        except TemplateError as e:
            raise TemplateRenderError(f"Jinja2模板渲染失败: {e}")
        except Exception as e:
            raise TemplateRenderError(f"模板渲染未知错误: {e}")
    
    def _render_step(self, step_template: StepTemplate, params: Dict[str, Any]) -> UnifiedStep:
        """
        渲染单个步骤模板。
        
        Args:
            step_template: 步骤模板
            params: 验证后的参数
            
        Returns:
            渲染后的UnifiedStep
        """
        try:
            # 渲染描述
            desc_template = self.env.from_string(step_template.description_template)
            description = desc_template.render(**params)
            
            # 渲染输入参数
            rendered_inputs = self._render_inputs(step_template.input_template, params)
            
            # 创建UnifiedStep
            step = UnifiedStep(
                step_id=step_template.template_step_id,
                name=step_template.name,
                description=description,
                tool_to_use=step_template.tool_to_use,
                inputs=rendered_inputs,
                dependencies=step_template.dependencies.copy(),
                max_retries=step_template.max_retries
            )
            
            return step
            
        except TemplateError as e:
            raise TemplateRenderError(f"步骤 {step_template.template_step_id} 渲染失败: {e}")
    
    def _render_inputs(self, input_template: Dict[str, Any], params: Dict[str, Any]) -> Dict[str, Any]:
        """
        渲染输入参数模板。
        
        Args:
            input_template: 输入模板字典
            params: 参数值
            
        Returns:
            渲染后的输入参数
        """
        rendered_inputs = {}
        
        for key, value in input_template.items():
            if isinstance(value, str):
                # 字符串值，使用Jinja2渲染
                template = self.env.from_string(value)
                rendered_inputs[key] = template.render(**params)
            elif isinstance(value, dict):
                # 嵌套字典，递归渲染
                rendered_inputs[key] = self._render_inputs(value, params)
            elif isinstance(value, list):
                # 列表，渲染每个元素
                rendered_inputs[key] = self._render_list(value, params)
            else:
                # 其他类型，直接使用
                rendered_inputs[key] = value
        
        return rendered_inputs
    
    def _render_list(self, template_list: List[Any], params: Dict[str, Any]) -> List[Any]:
        """
        渲染列表模板。
        
        Args:
            template_list: 模板列表
            params: 参数值
            
        Returns:
            渲染后的列表
        """
        rendered_list = []
        
        for item in template_list:
            if isinstance(item, str):
                template = self.env.from_string(item)
                rendered_list.append(template.render(**params))
            elif isinstance(item, dict):
                rendered_list.append(self._render_inputs(item, params))
            elif isinstance(item, list):
                rendered_list.append(self._render_list(item, params))
            else:
                rendered_list.append(item)
        
        return rendered_list
    
    def _validate_params(self, template: PlanTemplate, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证模板参数。
        
        Args:
            template: 计划模板
            params: 原始参数
            
        Returns:
            验证后的参数
            
        Raises:
            TemplateRenderError: 验证失败时抛出
        """
        validated_params = {}
        
        # 检查必需参数
        for param_name, schema in template.parameters.items():
            if schema.required and param_name not in params:
                raise TemplateRenderError(f"缺少必需参数: {param_name}")
        
        # 验证和转换参数
        for param_name, schema in template.parameters.items():
            if param_name in params:
                value = params[param_name]
                validated_value = self._validate_single_param(param_name, value, schema)
                validated_params[param_name] = validated_value
            elif schema.default is not None:
                validated_params[param_name] = schema.default
        
        # 添加额外的参数（不在schema中的）
        for param_name, value in params.items():
            if param_name not in template.parameters:
                validated_params[param_name] = value
        
        return validated_params
    
    def _validate_single_param(self, param_name: str, value: Any, schema: ParameterSchema) -> Any:
        """
        验证单个参数。
        
        Args:
            param_name: 参数名
            value: 参数值
            schema: 参数schema
            
        Returns:
            验证后的值
            
        Raises:
            TemplateRenderError: 验证失败时抛出
        """
        # 类型检查
        if schema.type == "string" and not isinstance(value, str):
            raise TemplateRenderError(f"参数 {param_name} 应为字符串类型，实际为 {type(value).__name__}")
        elif schema.type == "integer" and not isinstance(value, int):
            raise TemplateRenderError(f"参数 {param_name} 应为整数类型，实际为 {type(value).__name__}")
        elif schema.type == "float" and not isinstance(value, (int, float)):
            raise TemplateRenderError(f"参数 {param_name} 应为数字类型，实际为 {type(value).__name__}")
        elif schema.type == "boolean" and not isinstance(value, bool):
            raise TemplateRenderError(f"参数 {param_name} 应为布尔类型，实际为 {type(value).__name__}")
        elif schema.type == "list" and not isinstance(value, list):
            raise TemplateRenderError(f"参数 {param_name} 应为列表类型，实际为 {type(value).__name__}")
        elif schema.type == "dict" and not isinstance(value, dict):
            raise TemplateRenderError(f"参数 {param_name} 应为字典类型，实际为 {type(value).__name__}")
        
        # 选项检查
        if schema.options and value not in schema.options:
            raise TemplateRenderError(f"参数 {param_name} 的值 {value} 不在允许的选项中: {schema.options}")
        
        # 数值范围检查
        if schema.type in ["integer", "float"]:
            if schema.min_value is not None and value < schema.min_value:
                raise TemplateRenderError(f"参数 {param_name} 的值 {value} 小于最小值 {schema.min_value}")
            if schema.max_value is not None and value > schema.max_value:
                raise TemplateRenderError(f"参数 {param_name} 的值 {value} 大于最大值 {schema.max_value}")
        
        # 字符串模式检查
        if schema.type == "string" and schema.pattern:
            import re
            if not re.match(schema.pattern, value):
                raise TemplateRenderError(f"参数 {param_name} 的值 {value} 不匹配模式 {schema.pattern}")
        
        return value


# 全局渲染器实例
template_renderer = UnifiedTemplateRenderer()
