# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

"""
planning.py

This module contains the tools for creating and revising plans.
The Master Agent will use these tools to structure its approach to complex tasks.
"""

from typing import List, Dict, Any, Optional

from langchain_core.tools import tool
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.runnables import Runnable, RunnableConfig
from pydantic import BaseModel, Field
from langchain_core.output_parsers import PydanticOutputParser

from src.utils.prompt_utils import get_agent_prompt_template
from src.graph_v2.unified_models import UnifiedPlan
from src.graph_v2.models import Plan  # 保留用于向后兼容

# Global variable for the planner chain
_planner_chain: Optional[Runnable] = None
# Global variable for the reviser chain
_reviser_chain: Optional[Runnable] = None


class PlannerInput(BaseModel):
    task: str = Field(description="The user's request, which needs to be broken down into a structured plan.")
    # The plan field is optional. If a plan already exists (e.g., from a previous attempt),
    # it can be passed in for context, allowing the planner to refine or correct it.
    plan: Optional[str] = Field(default=None, description="An optional JSON string of the existing plan to be revised or refined.")


def _create_planner_chain(llm: Runnable) -> Runnable:
    """Creates the LLM chain for the planner tool."""
    # 🔧 使用Legacy Plan，然后在工具中转换为UnifiedPlan
    parser = PydanticOutputParser(pydantic_object=Plan)
    base_prompt = get_agent_prompt_template("planner_prompt_zh")
    prompt_template = base_prompt.partial(
        format_instructions=parser.get_format_instructions()
    )
    return prompt_template | llm | parser

def _create_reviser_chain(llm: Runnable) -> Runnable:
    """Creates the LLM chain for the reviser tool. (To be implemented fully later)"""
    # For now, this is a placeholder. The main logic resides in the planner.
    parser = PydanticOutputParser(pydantic_object=Plan)
    base_prompt = get_agent_prompt_template("reviser_prompt_zh")
    prompt_template = base_prompt.partial(
        format_instructions=parser.get_format_instructions()
    )
    return prompt_template | llm | parser

@tool("planner_tool", args_schema=PlannerInput)
def planner_tool(task: str, plan: Optional[str] = None) -> Dict[str, UnifiedPlan]:
    """
    Given a user's task, and optionally an existing plan, returns a structured UnifiedPlan object.

    If an existing plan is provided, the tool will attempt to revise it based on the
    context of the original task.
    """
    global _planner_chain
    if _planner_chain is None:
        raise ValueError("Planner chain not initialized. Please call initialize_planning_tools() first.")

    try:
        # The runnable configuration is now passed implicitly by the agent executor
        # We pass the plan as a simple string for the LLM to understand context
        legacy_plan = _planner_chain.invoke({"task": task, "plan": plan if plan else "No existing plan."})

        # The original task is not part of the LLM output, so we set it here.
        legacy_plan.original_task = task

        # 🔧 转换Legacy Plan为UnifiedPlan
        from src.graph_v2.migration_utils import migrate_legacy_plan_to_unified
        unified_plan = migrate_legacy_plan_to_unified(legacy_plan)

        print(f"📋 planner_tool生成计划: {len(unified_plan.steps)}个步骤")
        for i, step in enumerate(unified_plan.steps, 1):
            print(f"   {i}. {step.name} ({step.tool_to_use})")

        # Return the unified plan in a dictionary, so it can be merged back into the state
        return {"plan": unified_plan}

    except Exception as e:
        print(f"❌ planner_tool执行失败: {e}")
        # 返回一个空的UnifiedPlan作为fallback
        from src.graph_v2.unified_models import UnifiedPlan
        fallback_plan = UnifiedPlan(
            original_task=task,
            steps=[]
        )
        return {"plan": fallback_plan}

@tool("reviser_tool")
def reviser_tool() -> Dict[str, UnifiedPlan]:
    """
    (Placeholder) This tool is intended to revise a plan that has encountered an error.
    Currently, the main revision logic is handled by re-running the `planner_tool`
    with the failed plan as context.
    """
    # For now, we return an empty UnifiedPlan.
    # The master agent should be prompted to use the planner_tool for revisions.
    from src.graph_v2.unified_models import UnifiedPlan
    return {"plan": UnifiedPlan(original_task="Revision required", steps=[])}

def initialize_planning_tools(llm: Runnable):
    """
    Initializes the planner and reviser tools' LLM chains.
    This must be called before these tools can be used.
    """
    global _planner_chain, _reviser_chain
    _planner_chain = _create_planner_chain(llm)
    _reviser_chain = _create_reviser_chain(llm) 