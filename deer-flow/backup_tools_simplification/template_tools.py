# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

"""
template_tools.py

This module provides tools for the template system that can be used by the Master Agent.
These tools allow the agent to discover, recommend, and instantiate templates.
"""

from typing import Dict, Any, List, Optional
from langchain_core.tools import tool
from pydantic import BaseModel, Field
import json
import re
import uuid

from src.graph_v2.unified_models import UnifiedPlan, UnifiedStep
from src.graph_v2.template_models import (
    PlanTemplate,
    TemplateRecommendation,
    TemplateValidationResult,
    TemplateInstantiationResult
)
from src.graph_v2.template_renderer import template_renderer, TemplateRenderError
from src.graph_v2.types import State


# Global template registry (will be populated with built-in templates)
_template_registry: Dict[str, PlanTemplate] = {}


class RecommendTemplateInput(BaseModel):
    """Input schema for template recommendation."""
    user_input: str = Field(..., description="The user's original request or description")
    context: Optional[str] = Field(default=None, description="Additional context about the user's needs")


class GetTemplatesInput(BaseModel):
    """Input schema for getting available templates."""
    category: Optional[str] = Field(default=None, description="Filter by category (e.g., 'video_creation')")
    tags: Optional[List[str]] = Field(default=None, description="Filter by tags")
    difficulty: Optional[str] = Field(default=None, description="Filter by difficulty level")


class CreatePlanFromTemplateInput(BaseModel):
    """Input schema for creating a plan from a template."""
    template_id: str = Field(..., description="ID of the template to use")
    params: Dict[str, Any] = Field(..., description="Parameters for template instantiation")
    user_context: Optional[str] = Field(default=None, description="User's specific requirements or context")
    allow_customization: bool = Field(default=True, description="Whether to allow customization based on user context")


class ValidateTemplateParamsInput(BaseModel):
    """Input schema for validating template parameters."""
    template_id: str = Field(..., description="ID of the template")
    params: Dict[str, Any] = Field(..., description="Parameters to validate")


@tool("recommend_template", args_schema=RecommendTemplateInput)
def recommend_template(user_input: str, context: Optional[str] = None) -> Dict[str, Any]:
    """
    Analyze user input and recommend the most suitable template.
    
    This tool uses keyword matching and pattern recognition to suggest templates
    that best match the user's request.
    
    Args:
        user_input: The user's original request
        context: Additional context about the user's needs
        
    Returns:
        Dictionary containing recommendation details
    """
    try:
        # Simple keyword-based recommendation logic
        # In a production system, this could use ML models or more sophisticated matching
        
        user_text = (user_input + " " + (context or "")).lower()
        
        recommendations = []
        
        # Check for video creation keywords
        video_keywords = ["视频", "video", "鬼畜", "parody", "宣传", "promo", "广告", "ad", "mv", "短片"]
        if any(keyword in user_text for keyword in video_keywords):
            if any(word in user_text for word in ["鬼畜", "parody", "搞笑", "funny"]):
                recommendations.append({
                    "template_id": "ai_parody_video",
                    "confidence": 0.9,
                    "reason": "检测到鬼畜视频制作需求"
                })
            elif any(word in user_text for word in ["产品", "product", "宣传", "promo", "广告"]):
                recommendations.append({
                    "template_id": "product_promo_video", 
                    "confidence": 0.85,
                    "reason": "检测到产品宣传视频需求"
                })
        
        # Check for image series keywords
        image_keywords = ["海报", "poster", "系列", "series", "批量", "batch", "城市", "city"]
        if any(keyword in user_text for keyword in image_keywords):
            recommendations.append({
                "template_id": "city_poster_series",
                "confidence": 0.8,
                "reason": "检测到图片系列制作需求"
            })
        
        if not recommendations:
            return {
                "recommended_template": None,
                "confidence": 0.0,
                "reason": "未找到匹配的模板，建议使用自定义规划",
                "suggested_params": {},
                "alternatives": list(_template_registry.keys())
            }
        
        # Return the highest confidence recommendation
        best_rec = max(recommendations, key=lambda x: x["confidence"])
        
        # Try to extract suggested parameters
        suggested_params = _extract_suggested_params(user_text, best_rec["template_id"])
        
        return {
            "recommended_template": best_rec["template_id"],
            "confidence": best_rec["confidence"],
            "reason": best_rec["reason"],
            "suggested_params": suggested_params,
            "alternatives": [r["template_id"] for r in recommendations if r["template_id"] != best_rec["template_id"]]
        }
        
    except Exception as e:
        return {
            "recommended_template": None,
            "confidence": 0.0,
            "reason": f"推荐过程中出现错误: {str(e)}",
            "suggested_params": {},
            "alternatives": []
        }


@tool("get_available_templates", args_schema=GetTemplatesInput)
def get_available_templates(
    category: Optional[str] = None, 
    tags: Optional[List[str]] = None,
    difficulty: Optional[str] = None
) -> List[Dict[str, Any]]:
    """
    Get a list of available templates, optionally filtered by criteria.
    
    Args:
        category: Filter by template category
        tags: Filter by template tags
        difficulty: Filter by difficulty level
        
    Returns:
        List of template information dictionaries
    """
    try:
        templates = []
        
        for template_id, template in _template_registry.items():
            # Apply filters
            if category and template.category != category:
                continue
            if tags and not any(tag in template.tags for tag in tags):
                continue
            if difficulty and template.difficulty_level != difficulty:
                continue
            
            templates.append({
                "template_id": template.template_id,
                "name": template.name,
                "description": template.description,
                "category": template.category,
                "tags": template.tags,
                "difficulty_level": template.difficulty_level,
                "estimated_duration": template.estimated_duration,
                "usage_count": template.usage_count,
                "parameters": {
                    name: {
                        "type": schema.type.value,
                        "required": schema.required,
                        "default": schema.default,
                        "description": schema.description,
                        "options": schema.options
                    }
                    for name, schema in template.parameters.items()
                }
            })
        
        # Sort by usage count and success rate
        templates.sort(key=lambda x: x["usage_count"], reverse=True)
        
        return templates
        
    except Exception as e:
        return [{"error": f"获取模板列表时出现错误: {str(e)}"}]


@tool("create_plan_from_template", args_schema=CreatePlanFromTemplateInput)
def create_plan_from_template(
    template_id: str,
    params: Dict[str, Any],
    user_context: Optional[str] = None,
    allow_customization: bool = True
) -> Dict[str, Any]:
    """
    Create a concrete execution plan from a template.
    
    Args:
        template_id: ID of the template to use
        params: Parameters for template instantiation
        user_context: User's specific requirements
        allow_customization: Whether to allow customization based on context
        
    Returns:
        Dictionary containing the created plan or error information
    """
    try:
        # Get the template
        template = _template_registry.get(template_id)
        if not template:
            return {
                "success": False,
                "error": f"Template '{template_id}' not found",
                "plan": None
            }
        
        # 🔧 使用新的模板渲染器（更稳定可靠）
        try:
            plan = template_renderer.render_plan(template, params)

            # 如果提供了用户上下文，更新任务描述
            if user_context:
                plan.original_task = user_context

        except TemplateRenderError as e:
            return {
                "success": False,
                "error": f"模板渲染失败: {str(e)}",
                "plan": None
            }
        
        # Update template usage count
        template.usage_count += 1
        
        return {
            "success": True,
            "plan": plan,
            "error": None,
            "warnings": [],
            "metadata": {
                "template_id": template_id,
                "template_name": template.name,
                "parameters_used": params,
                "total_steps": len(plan.steps),
                "estimated_duration": template.estimated_duration
            }
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": f"创建计划时出现错误: {str(e)}",
            "plan": None
        }


@tool("validate_template_params", args_schema=ValidateTemplateParamsInput)
def validate_template_params(template_id: str, params: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate parameters for a specific template.
    
    Args:
        template_id: ID of the template
        params: Parameters to validate
        
    Returns:
        Validation result with errors and suggestions
    """
    try:
        template = _template_registry.get(template_id)
        if not template:
            return {
                "valid": False,
                "errors": [f"Template '{template_id}' not found"],
                "warnings": [],
                "suggestions": {}
            }
        
        try:
            validated_params = template.validate_parameters(params)
            return {
                "valid": True,
                "errors": [],
                "warnings": [],
                "suggestions": validated_params
            }
        except ValueError as e:
            return {
                "valid": False,
                "errors": [str(e)],
                "warnings": [],
                "suggestions": {}
            }
            
    except Exception as e:
        return {
            "valid": False,
            "errors": [f"验证过程中出现错误: {str(e)}"],
            "warnings": [],
            "suggestions": {}
        }


def _extract_suggested_params(user_text: str, template_id: str) -> Dict[str, Any]:
    """Extract suggested parameter values from user input."""
    suggested = {}
    
    if template_id == "ai_parody_video":
        # Try to extract character name
        character_patterns = [
            r"(哪吒|孙悟空|猪八戒|唐僧|白娘子|嫦娥)",
            r"([A-Za-z]+)\s*(?:鬼畜|parody|视频)"
        ]
        for pattern in character_patterns:
            match = re.search(pattern, user_text)
            if match:
                suggested["character"] = match.group(1)
                break
    
    elif template_id == "product_promo_video":
        # Try to extract product name
        product_patterns = [
            r"(产品|product)[:：]\s*([^，。,.\s]+)",
            r"([^，。,.\s]+)\s*(?:产品|product|宣传)"
        ]
        for pattern in product_patterns:
            match = re.search(pattern, user_text)
            if match:
                suggested["product_name"] = match.group(2) if len(match.groups()) > 1 else match.group(1)
                break
    
    return suggested


# 旧的渲染函数已移除，现在使用 UnifiedTemplateRenderer


def register_template(template: PlanTemplate):
    """Register a template in the global registry."""
    _template_registry[template.template_id] = template


def get_template_registry() -> Dict[str, PlanTemplate]:
    """Get the current template registry."""
    return _template_registry.copy()


# List of all template tools for easy import
all_template_tools = [
    recommend_template,
    get_available_templates,
    create_plan_from_template,
    validate_template_params
]
