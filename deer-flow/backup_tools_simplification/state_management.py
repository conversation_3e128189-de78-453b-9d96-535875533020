# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

"""
state_management.py

重新设计的状态管理工具，修复了原有的工具调用问题。

核心改进：
1. 移除了错误的state参数设计
2. 使用汇报机制让Master Agent报告执行状态
3. 通过返回值传递状态更新信息
"""
from typing import Dict, Any, List, Optional
from langchain_core.tools import tool
from pydantic import BaseModel, Field
import json
from datetime import datetime

from src.graph_v2.types import State
from src.graph_v2.unified_models import UnifiedPlan
from src.graph_v2.migration_utils import ensure_unified_plan


# ============================================================================
# 新的状态管理工具 - 修复了原有的设计问题
# ============================================================================

@tool
def report_step_completion(step_id: str, status: str, result_summary: str) -> str:
    """
    Master Agent用来汇报步骤完成情况的工具。

    这是修复后的状态管理机制的核心工具。Master Agent执行完步骤后，
    必须调用此工具来汇报执行结果。

    Args:
        step_id: 步骤ID
        status: 执行状态，"completed" 或 "failed"
        result_summary: 执行结果的简要描述

    Returns:
        确认消息
    """
    # 返回结构化的状态更新指令
    update_instruction = {
        "action": "update_step_status",
        "step_id": step_id,
        "status": status,
        "result_summary": result_summary,
        "timestamp": datetime.now().isoformat()
    }

    return f"✅ 步骤状态已汇报: {json.dumps(update_instruction, ensure_ascii=False)}"


@tool
def request_current_step_info() -> str:
    """
    请求获取当前应该执行的步骤信息。

    Master Agent可以调用此工具来获取当前需要执行的步骤详情。
    实际的步骤信息将由执行引擎通过消息提供。

    Returns:
        请求确认消息
    """
    return "📋 已请求当前步骤信息，请等待执行引擎提供详细指令。"


@tool
def request_plan_progress() -> str:
    """
    请求获取计划执行进度信息。

    Master Agent可以调用此工具来了解整体执行进度。

    Returns:
        请求确认消息
    """
    return "📊 已请求计划进度信息，执行引擎将提供详细进度报告。"


# ============================================================================
# 保留的原有工具（用于向后兼容，但标记为废弃）
# ============================================================================

@tool
def get_current_plan(state: State) -> str:
    """
    Retrieves the current plan from the state and returns it in a summary format.
    The agent can use this to review the overall plan and see the status of each step.
    Only supports EnhancedPlan models (legacy plans are automatically converted).
    """
    plan = state.get("plan")
    if not plan:
        return "No plan is currently available. You should probably create one first using the planner_tool."

    try:
        # Ensure we have a UnifiedPlan
        unified_plan = ensure_unified_plan(plan)

        # Provide detailed summary
        summary = {
            "plan_id": unified_plan.plan_id,
            "original_task": unified_plan.original_task,
            "total_steps": len(unified_plan.steps),
            "is_from_template": unified_plan.is_from_template,
            "source_template": unified_plan.template_id,
            "steps": [
                {
                    "step_id": step.step_id,
                    "name": step.name,
                    "description": step.description,
                    "tool_to_use": step.tool_to_use,
                    "status": step.status,
                    "dependencies": step.dependencies,
                    "max_retries": step.max_retries,
                    "retry_count": step.retry_count,
                    "user_friendly_status": step.get_user_friendly_status()
                }
                for step in unified_plan.steps
            ],
            "execution_summary": unified_plan.get_execution_summary(),
            "progress_info": unified_plan.get_progress_info(),
            "is_complete": unified_plan.is_complete(),
            "has_failed": unified_plan.has_failed()
        }
        return json.dumps(summary, indent=2, ensure_ascii=False)

    except Exception as e:
        return f"Error retrieving plan: {str(e)}"


@tool
def get_next_pending_step(state: State) -> str:
    """
    Finds the next step in the plan that is ready to be executed.
    A step is ready if its status is 'pending' and all its dependencies are 'completed'.
    Returns the step as a JSON string or a message if no steps are ready.
    Only supports EnhancedPlan models (legacy plans are automatically converted).
    """
    plan = state.get("plan")
    if not plan:
        return "No plan available. Cannot get a next step."

    try:
        # Ensure we have a UnifiedPlan
        unified_plan = ensure_unified_plan(plan)

        # Use the unified method
        executable_steps = unified_plan.get_next_executable_steps()

        if not executable_steps:
            if unified_plan.is_complete():
                return "Plan execution is complete. All steps have been finished."
            elif unified_plan.has_failed():
                failed_steps = [s for s in unified_plan.steps if s.status == "failed" and not s.should_retry()]
                retryable_steps = [s for s in unified_plan.steps if s.status == "failed" and s.should_retry()]
                return f"Plan has failed steps. {len(failed_steps)} failed, {len(retryable_steps)} can be retried."
            else:
                return "No steps are currently ready to execute. Check dependencies or step statuses."

        # Return the first executable step with enhanced information
        next_step = executable_steps[0]
        step_info = {
            "step_id": next_step.step_id,
            "name": next_step.name,
            "description": next_step.description,
            "tool_to_use": next_step.tool_to_use,
            "inputs": next_step.inputs,
            "status": next_step.status,
            "user_friendly_status": next_step.get_user_friendly_status(),
            "step_type": next_step.step_type.value,
            "dependencies": next_step.dependencies,
            "parallel_group": next_step.parallel_group,
            "estimated_duration": next_step.estimated_duration,
            "retry_count": next_step.retry_count,
            "max_retries": next_step.retry_config.max_retries
        }
        return json.dumps(step_info, indent=2, ensure_ascii=False)

    except Exception as e:
        return f"Error getting next step: {str(e)}"


class UpdateStepInput(BaseModel):
    step_id: str = Field(description="The unique ID of the step to update (string).")
    status: str = Field(description="The new status for the step.")
    result: Dict[str, Any] = Field(default_factory=dict, description="The output or result of the step's execution. This can be a success message, a file path, or an error description.")

@tool
def update_step_status(state: State, step_id: str, status: str, result: Dict[str, Any]) -> Dict[str, Any]:
    """
    Updates the status and result of a specific step in the plan.
    This is a critical tool for the agent to mark its progress after executing a step.
    It returns the updated plan object to be merged back into the state.
    Only supports EnhancedPlan models (legacy plans are automatically converted).
    """
    plan = state.get("plan")
    if not plan:
        return {"plan": None}

    try:
        # Ensure we have a UnifiedPlan
        unified_plan = ensure_unified_plan(plan)

        # Find and update the step
        step = unified_plan.get_step(step_id)
        if not step:
            return {"plan": unified_plan, "error": f"Step {step_id} not found"}

        # Update step status and result using UnifiedPlan methods
        old_status = step.status

        if status == "completed" and result:
            step.mark_completed(result)
            unified_plan.update_step_result(step_id, result)
        elif status == "failed":
            error_info = result if isinstance(result, dict) else {"error": str(result)}
            step.mark_failed(error_info)
        elif status == "in_progress":
            step.mark_started()
        else:
            # Manual status update for other cases
            step.status = status
            if result:
                step.result = result

        # UnifiedPlan handles progress tracking automatically
        return {"plan": unified_plan}

    except Exception as e:
        return {"plan": plan, "error": f"Error updating step status: {str(e)}"}


@tool
def check_execution_status(state: State) -> str:
    """
    检查当前执行状态，确定是否需要继续执行。
    这是Master Agent的执行完整性检查工具。

    Returns:
        执行状态报告，包括下一步行动建议
    """
    plan = state.get("plan")
    if not plan:
        return "❌ 无执行计划。建议：创建新计划或使用模板。"

    # Convert to UnifiedPlan for consistent handling
    unified_plan = ensure_unified_plan(plan)

    if unified_plan.is_complete():
        progress = unified_plan.get_progress_info()
        return f"✅ 计划执行完成！所有 {progress['completed_steps']} 个步骤都已完成。建议：向用户展示最终结果。"

    if unified_plan.has_failed():
        progress = unified_plan.get_progress_info()
        failed_steps = [s for s in unified_plan.steps if s.status == "failed" and not s.should_retry()]
        retryable_steps = [s for s in unified_plan.steps if s.status == "failed" and s.should_retry()]

        if retryable_steps:
            return f"⚠️ 有 {len(failed_steps)} 个步骤失败，其中 {len(retryable_steps)} 个可以重试。建议：继续执行或重试失败步骤。"
        else:
            return f"❌ 有 {len(failed_steps)} 个步骤失败且无法重试。建议：重新规划或修改计划。"

    # 检查下一个可执行步骤
    executable_steps = unified_plan.get_next_executable_steps()
    if executable_steps:
        next_step = executable_steps[0]
        progress = unified_plan.get_progress_info()
        return f"🔄 执行进行中：已完成 {progress['completed_steps']}/{progress['total_steps']} 步骤。下一步：{next_step.step_id} - {next_step.name}。建议：立即继续执行。"
    else:
        # 没有可执行步骤，检查是否有依赖问题
        pending_steps = [s for s in unified_plan.steps if s.status == "pending"]
        if pending_steps:
            return f"⏸️ 有 {len(pending_steps)} 个步骤等待中，但无法执行（可能有依赖问题）。建议：检查依赖关系或重新规划。"
        else:
            return "🤔 计划状态异常：没有待执行步骤但也未完成。建议：检查计划状态。"


@tool
def force_continue_execution(state: State) -> str:
    """
    强制检查并提醒继续执行。
    当Master Agent可能要停止时，使用此工具确保执行继续。

    Returns:
        强制继续执行的指令
    """
    status_check = check_execution_status.invoke({"state": state})

    if "✅ 计划执行完成" in status_check:
        return "计划已完成，可以停止执行。"
    elif "🔄 执行进行中" in status_check or "⚠️" in status_check:
        return f"⚠️ 执行尚未完成！{status_check}\n\n🚨 强制指令：立即调用 get_next_pending_step 继续执行，不要停止！"
    else:
        return f"需要处理执行问题：{status_check}"


@tool
def resolve_step_inputs(state: State, step_id: str) -> Dict[str, Any]:
    """
    Resolve step inputs by processing references to previous steps' outputs.

    This tool processes the inputs for a given step and resolves any references
    in the format {{step_X.field.path}} to actual values from completed steps.
    Only supports EnhancedPlan models (legacy plans are automatically converted).

    Args:
        state: The current graph state
        step_id: The ID of the step whose inputs should be resolved (string)

    Returns:
        Dict containing:
        - resolved_inputs: Dictionary of resolved input values
        - resolution_log: List of resolution operations performed
        - unresolved_references: List of references that couldn't be resolved
        - success: Boolean indicating if all references were resolved
    """
    plan = state.get("plan")
    if not plan:
        return {
            "resolved_inputs": {},
            "resolution_log": [],
            "unresolved_references": [],
            "success": False,
            "error": "No plan found in state"
        }

    try:
        # Ensure we have a UnifiedPlan
        unified_plan = ensure_unified_plan(plan)

        # Use the enhanced parameter resolver
        from src.graph_v2.parameter_resolver import ParameterResolverFactory

        resolved_inputs = ParameterResolverFactory.resolve_step_inputs(unified_plan, step_id)
        validation_results = ParameterResolverFactory.validate_plan_references(unified_plan)

        step_issues = validation_results.get(step_id, [])

        return {
            "resolved_inputs": resolved_inputs,
            "resolution_log": [f"Used enhanced parameter resolver for step {step_id}"],
            "unresolved_references": step_issues,
            "success": len(step_issues) == 0
        }

    except Exception as e:
        return {
            "resolved_inputs": {},
            "resolution_log": [],
            "unresolved_references": [str(e)],
            "success": False,
            "error": f"Parameter resolution failed: {str(e)}"
        }


@tool
def get_step_context(state: State, step_id: str) -> Dict[str, Any]:
    """
    Get comprehensive context information for a specific step.

    This tool provides complete context for a step, including its basic information,
    resolved inputs, available outputs from previous steps, and dependency analysis.

    Args:
        state: The current graph state
        step_id: The ID of the step to get context for

    Returns:
        Dict containing comprehensive step context information
    """
    plan = state.get("plan")
    if not plan:
        return {"error": "No plan found in state"}

    try:
        # Ensure we have a UnifiedPlan
        unified_plan = ensure_unified_plan(plan)

        # Find the target step
        target_step = unified_plan.get_step(step_id)
        if not target_step:
            return {"error": f"Step {step_id} not found in plan"}

    except Exception as e:
        return {"error": f"Error processing plan: {str(e)}"}

    # Get resolved inputs
    resolution_result = resolve_step_inputs.invoke({"state": state, "step_id": step_id})

    # Get available outputs from completed steps
    available_outputs = {}
    for step in unified_plan.steps:
        if step.status == "completed" and step.result:
            available_outputs[step.step_id] = step.result

    # Analyze dependencies (use the step's dependencies directly)
    dependencies = target_step.dependencies

    return {
        "step_info": {
            "step_id": target_step.step_id,
            "description": target_step.description,
            "tool_to_use": target_step.tool_to_use,
            "status": target_step.status,
            "inputs": target_step.inputs
        },
        "resolved_inputs": resolution_result.get("resolved_inputs", {}),
        "resolution_log": resolution_result.get("resolution_log", []),
        "unresolved_references": resolution_result.get("unresolved_references", []),
        "available_outputs": available_outputs,
        "dependencies": list(set(dependencies)),
        "is_ready": len(resolution_result.get("unresolved_references", [])) == 0
    }


# Legacy function removed - now using UnifiedPlan with parameter resolver


# 新的状态管理工具列表（推荐使用）
new_state_management_tools = [
    report_step_completion,
    request_current_step_info,
    request_plan_progress,
]

# 原有工具列表（保留用于向后兼容，但不推荐使用）
legacy_state_management_tools = [
    get_current_plan,
    get_next_pending_step,
    update_step_status,
    resolve_step_inputs,
    get_step_context,
    check_execution_status,
    force_continue_execution
]

# 默认使用新工具（可以通过配置切换）
state_management_tools = new_state_management_tools