#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
register_common_templates.py

注册常用模板，为use_template工具提供可用的模板。
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.graph_v2.template_models import PlanTemplate, StepTemplate, ParameterSchema, ParameterType
from src.tools.template_tools import register_template


def create_city_poster_template():
    """创建城市海报系列模板"""
    print("📝 创建城市海报系列模板...")
    
    # 参数定义
    params = {
        "cities": ParameterSchema(
            type=ParameterType.LIST,
            required=True,
            description="城市列表，如 ['巴黎', '纽约', '东京']"
        ),
        "style": ParameterSchema(
            type=ParameterType.STRING,
            required=False,
            default="minimalist",
            options=["minimalist", "vintage", "modern", "artistic"],
            description="海报风格"
        ),
        "color_scheme": ParameterSchema(
            type=ParameterType.STRING,
            required=False,
            default="blue_cream_coral",
            description="配色方案"
        )
    }
    
    # 步骤模板
    step_templates = [
        StepTemplate(
            template_step_id="create_style_guide",
            name="创建风格指南",
            description_template="为{{ style }}风格的城市海报系列创建统一的设计风格指南",
            tool_to_use="visual_expert",
            input_template={
                "task_description": "创建{{ style }}风格的海报设计风格指南，使用{{ color_scheme }}配色方案",
                "style": "{{ style }}",
                "color_scheme": "{{ color_scheme }}",
                "output_type": "style_guide"
            }
        ),
        StepTemplate(
            template_step_id="create_city_posters",
            name="批量创建城市海报",
            description_template="为{{ cities|length }}个城市创建{{ style }}风格海报",
            tool_to_use="visual_expert",
            input_template={
                "task_description": "基于风格指南，为以下城市创建海报：{{ cities|join(', ') }}",
                "cities": "{{ cities }}",
                "style": "{{ style }}",
                "color_scheme": "{{ color_scheme }}",
                "reference_style": "使用第一步的风格指南",
                "batch_mode": True
            },
            dependencies=["create_style_guide"]
        )
    ]
    
    template = PlanTemplate(
        template_id="city_poster_series",
        name="城市海报系列",
        description="为多个城市创建统一风格的海报系列",
        category="visual_design",
        tags=["poster", "city", "series", "design"],
        step_templates=step_templates,
        parameters=params,
        estimated_duration=1800  # 30分钟
    )
    
    register_template(template)
    print("   ✅ 城市海报系列模板已注册")


def create_ai_parody_video_template():
    """创建AI鬼畜视频模板"""
    print("📝 创建AI鬼畜视频模板...")
    
    # 参数定义
    params = {
        "character": ParameterSchema(
            type=ParameterType.STRING,
            required=True,
            description="主角角色名称，如 '哪吒'、'孙悟空'"
        ),
        "theme": ParameterSchema(
            type=ParameterType.STRING,
            required=False,
            default="funny",
            options=["funny", "epic", "cute", "dramatic"],
            description="视频主题风格"
        ),
        "duration": ParameterSchema(
            type=ParameterType.INTEGER,
            required=False,
            default=30,
            min_value=15,
            max_value=120,
            description="视频时长（秒）"
        )
    }
    
    # 步骤模板
    step_templates = [
        StepTemplate(
            template_step_id="collect_character_materials",
            name="收集角色素材",
            description_template="收集{{ character }}的{{ theme }}风格素材",
            tool_to_use="visual_expert",
            input_template={
                "task_description": "收集{{ character }}的图片素材，风格偏向{{ theme }}",
                "character": "{{ character }}",
                "theme": "{{ theme }}",
                "material_type": "character_images"
            }
        ),
        StepTemplate(
            template_step_id="generate_background_music",
            name="生成背景音乐",
            description_template="为{{ character }}鬼畜视频生成{{ theme }}风格背景音乐",
            tool_to_use="audio_expert",
            input_template={
                "task_description": "生成适合{{ character }}鬼畜视频的{{ theme }}风格背景音乐",
                "character": "{{ character }}",
                "theme": "{{ theme }}",
                "duration": "{{ duration }}",
                "music_style": "electronic_remix"
            }
        ),
        StepTemplate(
            template_step_id="create_parody_video",
            name="制作鬼畜视频",
            description_template="制作{{ character }}的{{ duration }}秒{{ theme }}风格鬼畜视频",
            tool_to_use="video_expert",
            input_template={
                "task_description": "制作{{ character }}的鬼畜视频，时长{{ duration }}秒",
                "character": "{{ character }}",
                "theme": "{{ theme }}",
                "duration": "{{ duration }}",
                "materials": "使用前面步骤的素材",
                "video_style": "parody_remix"
            },
            dependencies=["collect_character_materials", "generate_background_music"]
        )
    ]
    
    template = PlanTemplate(
        template_id="ai_parody_video",
        name="AI鬼畜视频制作",
        description="制作{{ character }}的{{ theme }}风格鬼畜视频",
        category="video_creation",
        tags=["video", "parody", "ai", "character"],
        step_templates=step_templates,
        parameters=params,
        estimated_duration=2400  # 40分钟
    )
    
    register_template(template)
    print("   ✅ AI鬼畜视频模板已注册")


def create_brand_design_template():
    """创建品牌设计套件模板"""
    print("📝 创建品牌设计套件模板...")
    
    # 参数定义
    params = {
        "brand_name": ParameterSchema(
            type=ParameterType.STRING,
            required=True,
            description="品牌名称"
        ),
        "industry": ParameterSchema(
            type=ParameterType.STRING,
            required=True,
            options=["tech", "food", "fashion", "education", "healthcare", "finance"],
            description="行业类型"
        ),
        "style": ParameterSchema(
            type=ParameterType.STRING,
            required=False,
            default="modern",
            options=["modern", "classic", "playful", "elegant", "bold"],
            description="设计风格"
        )
    }
    
    # 步骤模板
    step_templates = [
        StepTemplate(
            template_step_id="design_logo",
            name="设计Logo",
            description_template="为{{ brand_name }}设计{{ style }}风格的Logo",
            tool_to_use="visual_expert",
            input_template={
                "task_description": "为{{ industry }}行业的{{ brand_name }}品牌设计{{ style }}风格Logo",
                "brand_name": "{{ brand_name }}",
                "industry": "{{ industry }}",
                "style": "{{ style }}",
                "output_type": "logo"
            }
        ),
        StepTemplate(
            template_step_id="create_brand_poster",
            name="创建品牌海报",
            description_template="创建{{ brand_name }}的品牌宣传海报",
            tool_to_use="visual_expert",
            input_template={
                "task_description": "基于Logo设计，创建{{ brand_name }}的品牌宣传海报",
                "brand_name": "{{ brand_name }}",
                "industry": "{{ industry }}",
                "style": "{{ style }}",
                "reference_logo": "使用第一步的Logo设计",
                "output_type": "poster"
            },
            dependencies=["design_logo"]
        ),
        StepTemplate(
            template_step_id="create_brand_video",
            name="制作品牌视频",
            description_template="制作{{ brand_name }}的品牌宣传视频",
            tool_to_use="video_expert",
            input_template={
                "task_description": "制作{{ brand_name }}的品牌宣传视频，展示Logo和海报设计",
                "brand_name": "{{ brand_name }}",
                "industry": "{{ industry }}",
                "style": "{{ style }}",
                "materials": "使用前面步骤的Logo和海报",
                "video_type": "brand_showcase"
            },
            dependencies=["design_logo", "create_brand_poster"]
        )
    ]
    
    template = PlanTemplate(
        template_id="brand_design_suite",
        name="品牌设计套件",
        description="为{{ brand_name }}创建完整的品牌设计套件",
        category="brand_design",
        tags=["brand", "logo", "poster", "video", "design"],
        step_templates=step_templates,
        parameters=params,
        estimated_duration=3600  # 60分钟
    )
    
    register_template(template)
    print("   ✅ 品牌设计套件模板已注册")


def main():
    """注册所有常用模板"""
    print("🚀 开始注册常用模板\n")
    
    try:
        create_city_poster_template()
        create_ai_parody_video_template()
        create_brand_design_template()
        
        print("\n🎉 所有模板注册完成！")
        print("\n📋 已注册的模板：")
        
        from src.tools.template_tools import get_registered_templates
        templates = get_registered_templates()
        
        for template_id, template_name in templates.items():
            print(f"   • {template_id}: {template_name}")
        
        print(f"\n总计: {len(templates)} 个模板")
        
    except Exception as e:
        print(f"\n❌ 模板注册过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
